import 'package:get/get.dart';
import 'package:point_of_sale/core/routes/app_pages.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/orders_list_controller.dart';

import '../../../../../core/services/cash_data_source.dart';
import '../../../../../core/widgets/success_snack_bar.dart';
import '../../../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../../../settings/presentation/getx/controllers/settings_controller.dart';

class PaymentButtonActionController extends GetxController {
  final OrderController orderController = Get.find<OrderController>();
  final OrdersListController ordersListController =
      Get.find<OrdersListController>();
  final OrderDetailsController orderDetailsController =
      Get.find<OrderDetailsController>();
  final LoginController loginController = Get.find<LoginController>();
  final SettingsController settingsController = Get.find<SettingsController>();
  final cashDataSource = Get.find<CashDataSource>();
  Future<void> handlePayment() async {
    final localCart = orderController.localCart;
    final ordersList = ordersListController.ordersListModel.data;
    final currentOrderIndex = ordersListController.order.value;

    // Case 1: If local cart is not empty, fetch order and navigate to payment
    if (localCart.isNotEmpty) {
      List<Future> orderFutures = [];

      for (var item in localCart) {
        final productId = item['productId'] ?? '';
        final productName = item['productName'] ?? '';
        final price = item['price'] ?? '0';
        final qty = int.tryParse(item['qty']!) ?? 1;
        final unitPrice = double.tryParse(price) ?? 0;
        final subtotal = unitPrice * qty;
        // final invoiceData = cashDataSource.getInvoiceData();
        // final vatPercentage = invoiceData['vat'] as String;
        // final addTaxFromCache = invoiceData['addTax'] as bool;
        final totalPrice =
            // addTaxFromCache
            //     ? subtotal +
            //         (subtotal *
            //             double.parse(vatPercentage) /
            //             100) // First equation: add VAT
            //     :
            subtotal; // Second equation: price already includes VAT

        // If orders list is empty, create new order
        if (ordersList == null || ordersList.isEmpty || currentOrderIndex < 0) {
          failedSnaskBar('ordersListEmpty'.tr);
          return;
        } else {
          // Add to existing order
          orderFutures.add(
            orderController.fetchOrder(
              productId,
              productName,
              qty,
              price,
              totalPrice.toString(),
              ordersList[currentOrderIndex].id ?? 0,
              ordersList[currentOrderIndex].orderNo ?? '',
            ),
          );
        }
      }

      try {
        await Future.wait(orderFutures);
        await ordersListController.getOrdersList();

        // Clear local cart after successful order creation
        orderController.localCart.clear();

        final updatedOrdersList = ordersListController.ordersListModel.data;
        if (updatedOrdersList != null && updatedOrdersList.isNotEmpty) {
          final orderIndex = currentOrderIndex >= 0 &&
                  currentOrderIndex < updatedOrdersList.length
              ? currentOrderIndex
              : updatedOrdersList.length - 1;

          if (updatedOrdersList[orderIndex].items != null) {
            for (int i = 0;
                i < updatedOrdersList[orderIndex].items!.length;
                i++) {}
          }

          final serverTotal =
              updatedOrdersList[orderIndex].items?.fold(0.0, (sum, item) {
                    return sum +
                        (double.tryParse(item.totalPrice ?? '0') ?? 0.0);
                  }) ??
                  0.0;

          // Extract the base price from server total for payment controller
          // final invoiceData = cashDataSource.getInvoiceData();
          // final addTaxFromCache = invoiceData['addTax'] as bool;
          // final vatPercentage = double.parse(invoiceData['vat'] as String);

          // final basePriceForPayment = addTaxFromCache && serverTotal > 0
          //     ? serverTotal /
          //         (1 +
          //             (vatPercentage /
          //                 100)) // Extract base price from VAT-inclusive total
          //     : serverTotal; // Use as is if VAT wasn't added

          // print(
          //     'DEBUG NAV: Server total: $serverTotal, VAT%: $vatPercentage, addTax: $addTaxFromCache, Base price: $basePriceForPayment');

          Get.toNamed(
            Routes.paymentView,
            arguments: {
              'orderId': updatedOrdersList[orderIndex].id,
              'orderNumber': updatedOrdersList[orderIndex].orderNo,
              'totalPrice': serverTotal.round(),
            },
          );
        }
      } catch (error) {
        failedSnaskBar('faildToProcessTheOrder'.tr);
      }
    }
    // Case 2: If local cart is empty
    else {
      // Check if orders list and current order exists
      if (ordersList != null &&
          ordersList.isNotEmpty &&
          currentOrderIndex >= 0 &&
          currentOrderIndex < ordersList.length) {
        final currentOrder = ordersList[currentOrderIndex];

        // Check if order items exist and are not empty
        final hasItems =
            currentOrder.items != null && currentOrder.items!.isNotEmpty;
        final serverTotal = hasItems
            ? currentOrder.items!.fold(0.0, (sum, item) {
                return sum + (double.tryParse(item.totalPrice ?? '0') ?? 0.0);
              })
            : 0.0;

        if (!hasItems || serverTotal == 0) {
          failedSnaskBar('noItemsInTheCart'.tr);
          return;
        }

        // Extract the base price from server total for payment controller
        final invoiceData = cashDataSource.getInvoiceData();
        final addTaxFromCache = invoiceData['addTax'] as bool;
        final vatPercentage = double.parse(invoiceData['vat'] as String);

        final basePriceForPayment = addTaxFromCache && serverTotal > 0
            ? serverTotal /
                (1 +
                    (vatPercentage /
                        100)) // Extract base price from VAT-inclusive total
            : serverTotal; // Use as is if VAT wasn't added

        Get.toNamed(
          Routes.paymentView,
          arguments: {
            'orderId': currentOrder.id,
            'orderNumber': currentOrder.orderNo,
            'totalPrice': basePriceForPayment.round(),
          },
        );
        successSnackBar('itemAddedSuccessfully'.tr);
      } else {
        failedSnaskBar('noItemsInTheCart'.tr);
      }
    }
  }
}
