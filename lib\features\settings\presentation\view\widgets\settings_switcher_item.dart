import 'package:flutter/material.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../../../../core/utils/app_text_style.dart';
import '../../../../../core/utils/size_config.dart';

class SettingsSwitcherItem extends StatelessWidget {
  const SettingsSwitcherItem({
    super.key,
    required this.onTap,
    required this.height,
    required this.title,
    required this.animatedRotationTurns,
    required this.showValue,
    required this.title2,
    required this.switcherValue,
    required this.onChanged,
  });

  final void Function() onTap;
  final double height, animatedRotationTurns;
  final String title, title2;
  final bool showValue, switcherValue;
  final void Function(bool)? onChanged;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: height,
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.width(16),
        vertical: AppSize.height(12),
      ),
      margin: EdgeInsets.symmetric(
        horizontal: AppSize.width(16),
        vertical: AppSize.height(12),
      ),
      decoration: BoxDecoration(
        color: AppColors.primaryWithOpacity,
        borderRadius: BorderRadius.circular(12),
      ),
      curve: Curves.easeInOut,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: AppTextStyle.primary18800,
              ),
              InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(8),
                child: AnimatedRotation(
                  turns: animatedRotationTurns,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    Icons.keyboard_arrow_right,
                    color: AppColors.primaryColor,
                    size: AppSize.height(24),
                  ),
                ),
              ),
            ],
          ),
          if (showValue) ...[
            SizedBox(
              height: AppSize.height(16),
            ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    title2,
                    style: AppTextStyle.primary16800,
                  ),
                ),
                Switch(
                  value: switcherValue,
                  onChanged: onChanged,
                  activeColor: AppColors.primaryColor,
                ),
              ],
            )
          ],
        ],
      ),
    );
  }
}
