import 'package:get/get.dart';

import '../../../injection_controller.dart';
import '../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../home/<USER>/getx/controllers/orders_list_controller.dart';
import '../controllers/orders_controller.dart';

class OrdersBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<OrdersController>(
      () => OrdersController(),
    );
    Get.lazyPut<LoginController>(
      () => LoginController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<OrdersListController>(
      () => OrdersListController(
        InjectionController().getIt(),
      ),
    );
  }
}
