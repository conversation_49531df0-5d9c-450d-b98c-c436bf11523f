import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/settings/presentation/view/widgets/settings_switcher_item.dart';

import '../../../../../core/utils/size_config.dart';
import '../../getx/controllers/tables_activation_controller.dart';

class SettingsTablesAvtivationSection
    extends GetView<TablesActivationController> {
  const SettingsTablesAvtivationSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => SettingsSwitcherItem(
        onTap: controller.toggleTablesActiveSection,
        height: controller.showTablesActivation.isTrue
            ? AppSize.height(110)
            : AppSize.height(50),
        title: 'tablesSettings'.tr,
        animatedRotationTurns:
            controller.showTablesActivation.value ? 0.25 : 0.0,
        showValue: controller.showTablesActivation.value,
        title2: 'activeTables'.tr,
        switcherValue: controller.tablesActive.value,
        onChanged: (value) => controller.toggleTablesActivation(),
      ),
    );
  }
}
