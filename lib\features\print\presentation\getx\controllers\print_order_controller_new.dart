import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart' as esc_plus;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_order_web_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:screenshot/screenshot.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  /// Generate ESC/POS commands for image-based invoice printing
  Future<List<int>> _generateImageBasedEscPos(Uint8List imageBytes) async {
    try {
      // Initialize generator with 80mm paper size (576 pixels width)
      final profile = await esc_plus.CapabilityProfile.load();
      final generator = esc_plus.Generator(esc_plus.PaperSize.mm80, profile);
      List<int> bytes = [];

      // Decode the image
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode invoice image');
      }

      Get.log('Original image size: ${image.width}x${image.height}');

      // Resize image to fit thermal printer width (576 pixels for 80mm)
      // Maintain aspect ratio
      final maxWidth = 576;
      final aspectRatio = image.height / image.width;
      final targetHeight = (maxWidth * aspectRatio).round();

      final resizedImage = img.copyResize(image,
          width: maxWidth,
          height: targetHeight,
          interpolation: img.Interpolation.linear);

      Get.log(
          'Resized image size: ${resizedImage.width}x${resizedImage.height}');

      // Add the image to the print job
      bytes += generator.image(resizedImage, align: esc_plus.PosAlign.center);

      // Add some space after the image
      bytes += generator.emptyLines(2);

      // Cut the paper
      bytes += generator.cut();

      Get.log('Generated ${bytes.length} ESC/POS bytes from image');
      return bytes;
    } catch (e) {
      Get.log('Error generating image-based ESC/POS: $e');
      rethrow;
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    if (kIsWeb) {
      await printInvoiceWeb(controller);
      return;
    }

    // Check if IP printers are configured for mobile
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      // No IP printers configured, use web printing method (PDF)
      Get.log('No IP printers configured, using web printing method (PDF)');
      await printInvoiceWeb(controller);
      return;
    }

    // IP printers are available, use mobile printing
    await printInvoiceMobile(controller);
  }

  Future<void> printInvoiceWeb(PrintController controller) async {
    final webController = Get.put(PrintOrderWebController());
    await webController.printInvoice(controller);
  }

  /// Capture widget specifically for thermal printer (576px width)
  Future<Uint8List> _captureWidgetForThermalPrinter(
    Widget widget, {
    double pixelRatio = 2.0,
  }) async {
    try {
      Get.log('Capturing widget for thermal printer (576px width)');

      final captureContextWidget = widget;

      final imageBytes = await screenshotController.captureFromWidget(
        captureContextWidget,
        delay: const Duration(
            milliseconds: 200), // Slightly longer delay for mobile
        pixelRatio: pixelRatio,
        context: Get.context!,
      );

      Get.log(
          'Thermal printer widget capture completed - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget for thermal printer: $e');
      rethrow;
    }
  }

  Future<void> _printToSinglePrinter(String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing to $printerIP');
      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);
      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing to $printerIP: $e');
      rethrow;
    }
  }

  Future<void> printInvoiceMobile(PrintController controller) async {
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    Get.log(
        'Starting image-based print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Create the invoice widget
      final invoiceWidget = Invoice(controller: controller);

      // Capture the widget as an image optimized for thermal printer
      final imageBytes = await _captureWidgetForThermalPrinter(
        invoiceWidget,
        pixelRatio: 2.0, // Good balance between quality and file size
      );

      // Generate ESC/POS commands from the image
      final List<int> escPosBytes = await _generateImageBasedEscPos(imageBytes);

      Get.log(
          'Generated ${escPosBytes.length} bytes from image-based invoice for thermal printer');

      // Print to all configured printers
      final List<Future<void>> printTasks = [];
      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(printerIP, escPosBytes));
      }

      try {
        await Future.wait(printTasks);
        successSnackBar(
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
        );
      } catch (e) {
        failedSnaskBar(
          'Some printers failed. Check printer connections.',
        );
      }
    } catch (e) {
      Get.log('Mobile print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }
}
