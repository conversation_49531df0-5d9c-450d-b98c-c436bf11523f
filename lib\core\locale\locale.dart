import 'package:get/get.dart';

class MyLocale implements Translations {
  @override
  Map<String, Map<String, String>> get keys => {
        "ar": {
          "order": "طلب",
          "checkout": "الدفع",
          "actions": "إجراءات",
          "kitchenNote": "ملاحظة المطبخ",
          'total': 'الاجمالي',
          'search': 'ابحث...',
          'payment': 'الدفع',
          'sr': "%s ريال سعودي",
          'login': 'تسجيل الدخول',
          'loginSuccess': 'تم تسجيل الدخول بنجاح',
          'enterThePinCode': 'أدخل كود التحقق',
          'wrongPinCode': 'كود التحقق غير صحيح',
          'cancel': 'إلغاء',
          'confirm': 'تأكيد',
          'createSession': 'إنشاء جلسة',
          'openingCash': 'النقدية الافتتاحية',
          'openingNote': 'ملاحظة الافتتاح',
          'addNote': 'إضافة ملاحظة..........',
          'closeSession': 'إغلاق الجلسة',
          'closingRegister': 'إغلاق السجل',
          'cash': 'نقدي',
          'opening': 'الافتتاحية',
          'cashInOut': 'دخول / خروج النقد',
          'counted': 'المحسوب',
          'difference': 'الفرق',
          'card': 'بطاقة',
          'customerAccount': 'حساب العميل',
          'closingNote': 'إضافة ملاحظة الإغلاق...',
          'discard': 'تجاهل',
          'dailySale': 'المبيعات اليومية',
          'SR': 'ر.س',
          'cashCount': 'عد النقدية',
          'success': 'نجاح',
          'all': 'الكل',
          'chooseThePaymentMethod': 'اختر طريقة الدفع',
          'enterTheMountToBePaidFromTheOriginal':
              'أدخل المبلغ المراد دفعه من أصل',
          'notes': 'ملاحظات',
          'addNotes': 'إضافة ملاحظة جديدة..........',
          "orderIsEmpty": "Your order is empty. Please add items to proceed.",
          "invalidAmount": "المبلغ المدخل غير صحيح. يرجى إدخال مبلغ صحيح.",
          "welcome": "اهلا بك",
          "pleaseEnterThePassword": " , يرجى ادخال كلمة السر",
          "forgotPassword": "نسيت كلمة السر؟",
          "newOrder": "طلب جديد",
          "orderNumber": "رقم الطلب",
          "quantity": 'الكمية',
          "discount(%)": "الخصم(%)",
          "holding": 'الإنتظار',
          "customer": "العميل",
          "meal": "وجبة",
          "home": "الرئيسية",
          "orders": "الطلبات",
          "cashIn/out": "النقد الداخل/الخارج",
          "tablesManagement": "إدارة الطاولات",
          "setting": "الاعدادات",
          "logout": "تسجيل خروج",
          "qty": "الكمية",
          "price": "السعر",
          "discount": "الخصم",
          "apply": "تطبيق",
          "addKitchenNote": "إضافة ملاحظة المطبخ",
          "note": "ملاحظة",
          "reason": "السبب",
          "amount": "المبلغ",
          "cashIn": "الدفع النقدي",
          "cashOut": "السحب النقدي",
          "addCustomer": "إضافة عميل",
          "add": "إضافة",
          "edit": "تعديل",
          "delete": "حذف",
          "name": "الاسم",
          "address": "العنوان",
          "balance": "الرصيد",
          "street": "الشارع",
          "city": "المدينة",
          "contact": "الاتصال",
          "searchCustomers": "بحث عن العملاء",
          "title": "عنوان الـ",
          "addNewCustomer": "إضافة عميل جديد",
          "done": "تم",
          "error": "خطأ",
          "sessionCreatedsuccessfully": "تم إنشاء الجلسة بنجاح!",
          "customerAddedSuccessfully": "تم إضافة العميل بنجاح!",
          "enterTheValue": "أدخل القيمة",
          "cashAddSuccessfully": "تم إضافة النقد بنجاح",
          "sessionClosedSuccessfully": "تم إغلاق الجلسة بنجاح!",
          "itemDeletedSuccessfully": "تم حذف العنصر بنجاح",
          "itemUpdatedSuccessfully": "تم تحديث العنصر بنجاح",
          "theNotesAddedSuccessfully": "تمت إضافة الملاحظات بنجاح",
          "itemAddedSuccessfully": "تم إضافة العنصر بنجاح",
          "fillTheRequiredFields": "يرجى ملء الحقول المطلوبة",
          "logoutSuccessfully": "تم تسجيل الخروج بنجاح",
          "noItemsInTheCart": "لا توجد عناصر في السلة",
          "faildToProcessTheOrder":
              "فشل في معالجة الطلب. يرجى المحاولة مرة أخرى",
          "pleasePayTheAmount": "يرجى دفع المبلغ",
          "selectCustomerGroup": "اختر مجموعة العملاء",
          "loading": "جار التحميل...",
          "name(english)": "الاسم (بالإنجليزية)",
          "name(arabic)": "الاسم (بالعربية)",
          "enterYourName": "أدخل اسمك",
          "email": "البريد الإلكتروني",
          "enterYourEmail": "أدخل بريدك الإلكتروني",
          "phoneNumber": "رقم الهاتف",
          "enterYourPhone": "أدخل رقم هاتفك",
          "customerType": "نوع العميل",
          "priceList": "قائمة الأسعار",
          "customerGroup": "مجموعة العملاء",
          "generalNote": "ملاحظة عامة",
          "quotationOrder": "اقتباس/طلب",
          "split": "تقسيم",
          "transferMerge": "نقل/دمج",
          "guests": "الضيوف",
          "customerNote": "ملاحظة العميل",
          "refund": "استرداد",
          "tax": "الضريبة",
          "cancelOrder": "إلغاء الطلب",
          "addressName": "اسم العنوان",
          "buildingNumber": "رقم المبنى",
          "apartmentNumber": "رقم الشقة",
          "riyadh": "الرياض",
          "region": "المنطقة",
          "zipCode": "الرمز البريدي",
          "discription": "الوصف",
          "selectAPriceList": "اختر قائمة الأسعار",
          "company": "الشركة",
          "individuals": "أفراد",
          "noMeals": "لا توجد وجبات",
          "wait": "انتظار",
          "toServe": "للتقديم",
          "emergency": "حالة طارئة",
          "noDressing": "لا يوجد تتبيل",
          "empty": "فارغ",
          "somethingWentWrongPleaseTryAgainLater":
              "حدث خطأ ما. يرجى المحاولة مرة أخرى لاحقًا.",
          "remaining": "المتبقي",
          "paymentSummary": "ملخص الدفع",
          "totalPaid": "إجمالي المدفوع",
          "paidAmount": "المبلغ المدفوع",
          "back": "رجوع",
          "paymentWith": "الدفع بواسطة",
          "paymentSuccess": "تم الدفع بنجاح",
          "printFullReceipt": "طباعة الإيصال الكامل",
          "pleaseSelectATeble": "يرجى اختيار طاولة",
          "date": "التاريخ",
          "startTime": "وقت البدء",
          "table": "طاولة",
          "addNewReservation": "إضافة حجز جديد",
          "guest": "عدد الضيوف",
          "orderType": "اختر نوع الطلب",
          "takeAway": "أخذ بعيدا",
          "dineIn": "في المطعم",
          "manageTables": "إدارة الطاولات",
          "received": "تم الاستلام",
          "availabled": "متوفر",
          "onDine": "في المطعم",
          "tables": "طاولات",
          "connectionTimeout": "انتهت مهلة الاتصال بخادم API",
          "sendTimeout": "انتهت مهلة الإرسال إلى خادم API",
          "receiveTimeout": "انتهت مهلة الاستقبال من خادم API",
          "badCertificate": "شهادة غير صالحة مع خادم API",
          "badResponse": "استجابة سيئة من الخادم",
          "requestCanceled": "تم إلغاء الطلب إلى خادم API",
          "noInternetConnection": "لا يوجد اتصال بالإنترنت",
          "unknownError": "عذراً! حدث خطأ. يرجى المحاولة مرة أخرى",
          "userNotFound":
              "المستخدم غير موجود. يرجى التحقق من بيانات الاعتماد الخاصة بك.",
          "serverProblem": "هناك مشكلة في الخادم. يرجى المحاولة لاحقاً.",
          "generalError": "حدث خطأ. يرجى المحاولة مرة أخرى.",
          "tableNotAvailable": "الطاولة غير متوفرة",
          "validate": "تحقق",
          "noPaymentMethodSelected": "لم يتم اختيار طريقة الدفع",
          "ordersListEmpty": "قائمة الطلبات فارغة. يرجى إنشاء طلب أولاً.",
          "settings": "الإعدادات",
          "appearance": "المظهر",
          "dark_mode": "الوضع الداكن",
          "language": "اللغة",
          "cancelReservation": "إلغاء الحجز",
          "finishReservation": "إنهاء الطاولة",
          "there'sNoReservationsForThisDay": "لا يوجد حجز لهذا اليوم",
          "thereIsNoReservationsForThisDay": "لا يوجد حجز لهذا اليوم",
          "reservedBy": "محجوزة بواسطة",
          "new": "جديد",
          "completed": "مكتمل",
          "canceled": "تم إلغائه",
          "noAvailableTables": "لا توجد جداول متاحة",
          "enterTheCountOfGuests": "أدخل عدد الضيوف",
          "reservationCompletedSuccessfully": "تم إكمال الحجز بنجاح",
          "reservationCanceledSuccessfully": "تم إلغاء الحجز بنجاح",
          "selectTimeSlot": "اختر وقت الحجز",
          "reservationAddedSuccessfully": "تم إضافة الحجز بنجاح",
          "thisSlotIsOutOfRange": "هذا الوقت خارج النطاق",
          "simpifiedTaxInvoice": "فاتورة ضريبة القيمة المضافة",
          "noOrders": "لا توجد طلبات متاحة",
          "orderCreatedSuccessfully": "تم إنشاء الطلب بنجاح",
          "areYouSureYouWantToRemoveThisPrinter":
              "هل أنت متأكد من رغبتك في إزالة هذا الطابعة؟",
          "?": "؟",
          "deletePrinter": "حذف الطابعة",
          "testConnection": "اختبار الاتصال",
          "networkPrinter": "طابعة شبكة",
          "addAPrinterIPAddressAboveToGetStarted":
              "أضف عنوان IP لطابعة الشبكة أعلاه للبدء",
          "noPrintersConfigured": "لا توجد طابعات محددة",
          "pleaseEnterAvalidIPAddress": "يرجى إدخال عنوان IP صالح",
          "e.g.": "مثلاً",
          "printerIPAddress": "عنوان IP لطابعة الشبكة",
          "addNewPrinter": "إضافة طابعة جديدة",
          "configuredPrinters": "الطباعة المحددة",
          "printerSettings": "إعدادات الطابعة",
          "taxingSettings": "إعدادات الضريبة",
          "addTax": "إضافة ضريبة",
          "tablesSettings": "إعدادات الطاولات",
          "activeTables": "تفعيل الطاولات",
          "inProgress": "جاري العمل عليه",
          "pending": "معلق",
        },
        "en": {
          "order": "Order",
          "checkout": "Checkout",
          "actions": "Actions",
          "kitchenNote": "Kitchen Note",
          'total': 'Total',
          'search': 'Search...',
          'payment': 'Payment',
          'sr': "SR %s",
          'login': 'Login',
          'loginSuccess': 'Login Successful',
          'enterThePinCode': 'Enter the Pin Code',
          'wrongPinCode': 'Wrong Pin Code',
          'cancel': 'Cancel',
          'confirm': 'Confirm',
          'createSession': 'Create Session',
          'openingCash': 'Opening Cash',
          'openingNote': 'Opening Note',
          'addNote': 'Add Note......',
          'closeSession': 'Close Session',
          'closingRegister': 'Closing Register',
          'cash': 'Cash',
          'opening': 'Opening',
          'cashInOut': 'Cash In / Out',
          'counted': 'Counted',
          'difference': 'Difference',
          'card': 'Card',
          'customerAccount': 'Customer Account',
          'closingNote': 'Add a closing note...',
          'discard': 'Discard',
          'dailySale': 'Daily Sale',
          'SR': 'SR',
          'cashCount': 'Cash Count',
          'success': 'Success',
          'all': 'All',
          'chooseThePaymentMethod': 'Choose the Payment Method',
          'enterTheMountToBePaidFromTheOriginal':
              'Enter the mount to be paid from the Original',
          'notes': 'Notes',
          'addNotes': 'Add a new note.........',
          "orderIsEmpty": "Your order is empty. Please add items to proceed.",
          "invalidAmount":
              "Invalid amount entered. Please enter a valid amount.",
          "welcome": "Welcome",
          "pleaseEnterThePassword": " , Please enter the password",
          "forgotPassword": "Forgot password?",
          "newOrder": "New order",
          "orderNumber": "Order Number",
          "quantity": 'Quantity',
          "discount(%)": "Discount(%)",
          "holding": "Holding",
          "customer": "Customer",
          "meal": "Meal",
          "home": "Home",
          "orders": "Orders",
          "cashIn/out": "Cash In/Out",
          "tablesManagement": "Tables Management",
          "setting": "Settings",
          "logout": "Logout",
          "qty": "Qty",
          "price": "Price",
          "discount": "Discount",
          "apply": "Apply",
          "addKitchenNote": "Add Kitchen Note",
          "note": "Note",
          "reason": "Reason",
          "amount": "Amount",
          "cashIn": "Cash In",
          "cashOut": "Cash Out",
          "addCustomer": "Add Customer",
          "add": "Add",
          "edit": "Edit",
          "delete": "Delete",
          "name": "Name",
          "address": "Address",
          "balance": "Balance",
          "street": "Street",
          "city": "City",
          "contact": "Contact",
          "searchCustomers": "Search Customers",
          "title": "Title",
          "addNewCustomer": "Add New Customer",
          "done": "Done",
          "error": "Error",
          "sessionCreatedsuccessfully": "Session created successfully!",
          "customerAddedSuccessfully": "Customer added successfully!",
          "enterTheValue": "Enter the value",
          "cashAddSuccessfully": "Cash added successfully",
          "sessionClosedSuccessfully": "Session closed successfully!",
          "itemDeletedSuccessfully": "Item deleted successfully",
          "itemUpdatedSuccessfully": "Item updated successfully",
          "theNotesAddedSuccessfully": "The notes added successfully",
          "itemAddedSuccessfully": "Item added successfully",
          "fillTheRequiredFields": "Please fill in the required fields",
          "logoutSuccessfully": "Logout successfully",
          "noItemsInTheCart": "No items in the cart.",
          "faildToProcessTheOrder":
              "Failed to process the order. Please try again",
          "pleasePayTheAmount": "Please pay the amount",
          "selectCustomerGroup": "Select Customer Group",
          "loading": "Loading...",
          "name(english)": "Name (English)",
          "name(arabic)": "Name (Arabic)",
          "enterYourName": "Enter Your Name",
          "email": "Email",
          "enterYourEmail": "Enter Your Email",
          "phoneNumber": "Phone Number",
          "enterYourPhone": "Enter Your Phone Number",
          "customerType": "Customer Type",
          "priceList": "Price List",
          "customerGroup": "Customer Group",
          "generalNote": "General Note",
          "quotationOrder": "Quotation/Order",
          "split": "Split",
          "transferMerge": "Transfer/Merge",
          "guests": "Guests",
          "customerNote": "Customer Note",
          "refund": "Refund",
          "tax": "Tax",
          "cancelOrder": "Cancel Order",
          "addressName": "Address Name",
          "buildingNumber": "Building Number",
          "apartmentNumber": "Apartment Number",
          "riyadh": "Riyadh",
          "region": "Region",
          "zipCode": "Zip Code",
          "discription": "Description",
          "selectAPriceList": "Select a price list",
          "company": "Company",
          "individuals": "Individuals",
          "noMeals": "No Meals",
          "wait": "Wait",
          "toServe": "To Serve",
          "emergency": "Emergency",
          "noDressing": "No Dressing",
          "empty": "Empty",
          "somethingWentWrongPleaseTryAgainLater":
              "Something went wrong. Please try again later.",
          "remaining": "Remaining",
          "paymentSummary": "Payment Summary",
          "totalPaid": "Total Paid",
          "paidAmount": "Paid Amount",
          "back": "Back",
          "paymentWith": "Payment with",
          "paymentSuccess": "Payment Success",
          "printFullReceipt": "Print Full Receipt",
          "pleaseSelectATeble": "Please select a table",
          "date": "Date",
          "startTime": "Start Time",
          "table": "Table",
          "addNewReservation": "Add New Reservation",
          "guest": "Guest",
          "orderType": "Choose Order Type",
          "takeAway": "Take Away",
          "dineIn": "Dine In",
          "manageTables": "Manage Tables",
          "received": "Received",
          "availabled": "Available",
          "onDine": "On Dine",
          "tables": "Tables",
          "connectionTimeout": "Connection timeout with API server",
          "sendTimeout": "Send timeout with API server",
          "receiveTimeout": "Receive timeout with API server",
          "badCertificate": "Bad certificate with API server",
          "badResponse": "Bad response from server",
          "requestCanceled": "Request to API server was canceled",
          "noInternetConnection": "No Internet Connection",
          "unknownError": "Oops! There was an error. Please try again",
          "userNotFound": "User not found. Please check your credentials.",
          "serverProblem":
              "There is a problem with the server. Please try later.",
          "generalError": "There was an error. Please try again.",
          "tableNotAvailable": "Table not available",
          "validate": "Validate",
          "noPaymentMethodSelected": "No payment method selected",
          "ordersListEmpty":
              "Orders list is empty. Please create an order first.",
          "settings": "Settings",
          "appearance": "Appearance",
          "dark_mode": "Dark Mode",
          "language": "Language",
          "cancelReservation": "Cancel Reservation",
          "finishReservation": "Finish Reservation",
          "there'sNoReservationsForThisDay":
              "There's no reservations for this day",
          "thereIsNoReservationsForThisDay":
              "There's no reservations for this day",
          "reservedBy": "Reserved By",
          "new": "New",
          "completed": "Completed",
          "canceled": "Canceled",
          "noAvailableTables": "No Available Tables",
          "enterTheCountOfGuests": "Enter the number of guests",
          "reservationCompletedSuccessfully":
              "Reservation completed successfully",
          "reservationCanceledSuccessfully":
              "Reservation canceled successfully",
          "selectTimeSlot": "Select Time Slot",
          "reservationAddedSuccessfully": "Reservation added successfully",
          "thisSlotIsOutOfRange": "This slot is out of range",
          "simpifiedTaxInvoice": "Simplified Tax Invoice",
          "noOrders": "No orders available",
          "orderCreatedSuccessfully": "Order created successfully",
          "areYouSureYouWantToRemoveThisPrinter":
              "Are you sure you want to remove this printer?",
          "?": "?",
          "deletePrinter": "Delete Printer",
          "testConnection": "Test Connection",
          "networkPrinter": "Network Printer",
          "addAPrinterIPAddressAboveToGetStarted":
              "Add a printer IP address above to get started",
          "noPrintersConfigured": "No printers configured",
          "pleaseEnterAvalidIPAddress": "Please enter a valid IP address",
          "e.g.": "e.g.",
          "printerIPAddress": "Printer IP Address",
          "addNewPrinter": "Add New Printer",
          "configuredPrinters": "Configured Printers",
          "printerSettings": "Printer Settings",
          "taxingSettings": "Taxing Settings",
          "addTax": "Add Tax",
          "tablesSettings": "Tables Settings",
          "activeTables": "Active Tables",
          "inProgress": "In Progress",
          "pending": "Pending",
        },
      };
}
