import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';
import 'package:point_of_sale/features/settings/presentation/view/widgets/settings_switcher_item.dart';

import '../../../../../core/utils/size_config.dart';

class SettingsAddTexSection extends GetView<SettingsController> {
  const SettingsAddTexSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => SettingsSwitcherItem(
        onTap: controller.toggleTaxingSettings,
        height: controller.showTaxingSettings.isTrue
            ? AppSize.height(110)
            : AppSize.height(50),
        title: 'taxingSettings'.tr,
        animatedRotationTurns: controller.showTaxingSettings.value ? 0.25 : 0.0,
        showValue: controller.showTaxingSettings.value,
        title2: 'addTax'.tr,
        switcherValue: controller.addTax.value,
        onChanged: (value) => controller.toggleAddTax(),
      ),
    );
  }
}
