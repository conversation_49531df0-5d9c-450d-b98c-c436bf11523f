import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/utils/app_assets.dart';
import '../../../core/utils/app_colors.dart';
import '../../../core/utils/app_text_style.dart';
import '../../../core/utils/size_config.dart';
import '../../../core/utils/size_utils.dart';
import '../../home/<USER>/views/widgets/app_bar_search.dart';
import '../../home/<USER>/views/widgets/custom_drawer.dart';
import '../controllers/orders_controller.dart';

class OrdersView extends GetView<OrdersController> {
  const OrdersView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        shape: const Border(
          bottom: BorderSide(
            color: AppColors.grey1,
            width: 1,
          ),
        ),
        toolbarHeight: AppSize.height(85),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.background,
        surfaceTintColor: AppColors.background,
        automaticallyImplyLeading: false,
        leadingWidth: AppSize.width(270),
        centerTitle: true,
        title: SizedBox(
          height: AppSize.height(36),
          width: AppSize.width(534),
          child: const AppBarSearch(),
        ),
        leading: Row(
          children: [
            SizedBox(
              width: AppSize.width(50),
            ),
            Builder(
              builder: (context) {
                return IconButton(
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                  icon: Icon(
                    Icons.menu,
                    size: getSize(32),
                    color: AppColors.green,
                  ),
                );
              },
            ),
            SizedBox(
              width: AppSize.width(18),
            ),
            Container(
              height: AppSize.height(34),
              width: AppSize.height(34),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.darkGreen,
              ),
              child: Center(
                child: Text(
                  controller.loginController.userName.value.isNotEmpty
                      ? controller.loginController.userName.value
                          .substring(0, 1)
                      : '',
                  style: AppTextStyle.white17600,
                ),
              ),
            ),
            SizedBox(
              width: AppSize.width(15),
            ),
            Text(
              controller.loginController.userName.value.isNotEmpty
                  ? controller.loginController.userName.value
                  : '',
              style: AppTextStyle.darkgreen14600,
            ),
          ],
        ),
        actions: [
          Padding(
            padding: EdgeInsetsDirectional.only(
              end: AppSize.width(51),
            ),
            child: Image.asset(
              AppAssets.appBarLogo,
              height: AppSize.height(50),
            ),
          ),
        ],
      ),
      drawer: Drawer(
        backgroundColor: AppColors.white,
        width: AppSize.width(372),
        child: const CustomDrawer(),
      ),
      body: Padding(
        padding: EdgeInsetsDirectional.symmetric(
          horizontal: AppSize.width(47),
          vertical: AppSize.height(15),
        ),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsetsGeometry.symmetric(
                horizontal: AppSize.width(10),
              ),
              child: Row(
                children: [
                  SizedBox(
                    height: AppSize.height(37),
                    width: AppSize.width(580),
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemCount: controller.orderStateList.length,
                      separatorBuilder: (BuildContext context, int index) {
                        return SizedBox(
                          width: AppSize.width(5),
                        );
                      },
                      itemBuilder: (BuildContext context, int index) {
                        return Obx(
                          () => InkWell(
                            onTap: () {
                              controller.changeOrderStateIndex(index);
                            },
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              height: AppSize.height(37),
                              width: AppSize.width(92),
                              decoration: BoxDecoration(
                                color: controller.orderStateIndex.value == index
                                    ? AppColors.primaryColor
                                    : AppColors.white,
                                border: Border.all(
                                  color:
                                      controller.orderStateIndex.value == index
                                          ? AppColors.primaryColor
                                          : AppColors.lavenderGray,
                                ),
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(6),
                                  topRight: Radius.circular(6),
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  controller.orderStateList[index],
                                  style:
                                      controller.orderStateIndex.value == index
                                          ? AppTextStyle.white14700
                                          : AppTextStyle.primary14700,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  Spacer(),
                  Obx(
                    () => InkWell(
                      onTap: () {
                        controller.changeOrderTypeIndex(0);
                      },
                      child: RichText(
                        text: TextSpan(
                          text: 'all'.tr,
                          style: controller.orderTypeIndex.value == 0
                              ? AppTextStyle.green14800.copyWith(
                                  decoration: TextDecoration.underline)
                              : AppTextStyle.primary14700,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: AppSize.width(12),
                  ),
                  Obx(
                    () => InkWell(
                      onTap: () {
                        controller.changeOrderTypeIndex(1);
                      },
                      child: RichText(
                        text: TextSpan(
                          text: 'takeAway'.tr,
                          style: controller.orderTypeIndex.value == 1
                              ? AppTextStyle.green14800.copyWith(
                                  decoration: TextDecoration.underline)
                              : AppTextStyle.primary14700,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: AppSize.width(12),
                  ),
                  Obx(
                    () => InkWell(
                      onTap: () {
                        controller.changeOrderTypeIndex(2);
                      },
                      child: RichText(
                        text: TextSpan(
                          text: 'dineIn'.tr,
                          style: controller.orderTypeIndex.value == 2
                              ? AppTextStyle.green14800.copyWith(
                                  decoration: TextDecoration.underline)
                              : AppTextStyle.primary14700,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.lavenderGray,
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    mainAxisSpacing: AppSize.height(24),
                    crossAxisSpacing: AppSize.width(24),
                    crossAxisCount: 4,
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: AppSize.height(24),
                    horizontal: AppSize.width(24),
                  ),
                  itemCount: controller
                          .ordersListController.ordersListModel.data?.length ??
                      0,
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.lavenderGray,
                        ),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Stack(
                        children: [
                          Align(
                            alignment: AlignmentDirectional.topStart,
                            child: Container(
                              height: AppSize.height(31),
                              width: AppSize.width(77),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadiusDirectional.only(
                                  topStart: Radius.circular(10),
                                  bottomEnd: Radius.circular(10),
                                ),
                                color: AppColors.primaryWithOpacity3,
                              ),
                              child: Center(
                                child: Text(
                                  controller
                                              .ordersListController
                                              .ordersListModel
                                              .data?[index]
                                              .typeId ==
                                          1
                                      ? 'takeAway'.tr
                                      : 'dineIn'.tr,
                                  style: AppTextStyle.primary12800,
                                ),
                              ),
                            ),
                          ),
                          Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '#${controller.ordersListController.ordersListModel.data?[index].orderNo ?? ''}',
                                  style: AppTextStyle.primary14700,
                                ),
                                Padding(
                                  padding: EdgeInsetsGeometry.only(
                                    top: AppSize.height(10),
                                    left: AppSize.width(10),
                                    right: AppSize.width(10),
                                    bottom: AppSize.height(18),
                                  ),
                                  child: SizedBox(
                                    height: AppSize.height(91),
                                    child: ListView.separated(
                                      itemCount: controller
                                              .ordersListController
                                              .ordersListModel
                                              .data?[index]
                                              .items
                                              ?.length ??
                                          0,
                                      separatorBuilder:
                                          (BuildContext context, int index) {
                                        return Divider(
                                          height: AppSize.height(7),
                                          color: AppColors.lavenderGray2,
                                        );
                                      },
                                      itemBuilder: (BuildContext context,
                                          int itemsIndexndex) {
                                        return Row(
                                          children: [
                                            Text(
                                              controller
                                                      .ordersListController
                                                      .ordersListModel
                                                      .data?[index]
                                                      .items?[itemsIndexndex]
                                                      .productName ??
                                                  '',
                                              style: AppTextStyle.primary12800,
                                            ),
                                            Spacer(),
                                            Text(
                                              'X',
                                              style: AppTextStyle.primary12700,
                                            ),
                                            SizedBox(
                                              width: AppSize.width(3),
                                            ),
                                            Container(
                                              height: AppSize.height(12),
                                              width: AppSize.height(12),
                                              decoration: BoxDecoration(
                                                color: AppColors.third,
                                              ),
                                              child: Center(
                                                child: Text(
                                                  controller
                                                          .ordersListController
                                                          .ordersListModel
                                                          .data?[index]
                                                          .items?[index]
                                                          .qty ??
                                                      '',
                                                  style:
                                                      AppTextStyle.primary10700,
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ),
                                Container(
                                  height: AppSize.height(43),
                                  decoration: BoxDecoration(
                                    color: AppColors.tablesBackground,
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      vertical: AppSize.height(6),
                                      horizontal: AppSize.width(10),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'customerName'.tr,
                                              style: AppTextStyle.primary12800,
                                            ),
                                            Spacer(),
                                            Text(
                                              controller
                                                      .ordersListController
                                                      .ordersListModel
                                                      .data?[index]
                                                      .customerName ??
                                                  '',
                                              style: AppTextStyle.darkGray11600,
                                            ),
                                          ],
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'total'.tr,
                                              style: AppTextStyle.primary12800,
                                            ),
                                            Spacer(),
                                            Text(
                                              '${controller.ordersListController.ordersListModel.data?[index].totalAmount ?? ''} ${'SR'.tr}',
                                              style: AppTextStyle.darkGray11600,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                    top: AppSize.height(12),
                                    right: AppSize.width(10),
                                    left: AppSize.width(10),
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        height: AppSize.height(21),
                                        width: AppSize.width(39),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color: AppColors.primaryColor),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Center(
                                          child: Icon(
                                            Icons.print,
                                            size: getSize(14),
                                            color: AppColors.primaryColor,
                                          ),
                                        ),
                                      ),
                                      Spacer(),
                                      Container(
                                        height: AppSize.height(21),
                                        width: AppSize.width(65),
                                        decoration: BoxDecoration(
                                          color: AppColors.frostedMint,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Center(
                                          child: Text(
                                            controller
                                                        .ordersListController
                                                        .ordersListModel
                                                        .data?[index]
                                                        .statusId ==
                                                    1
                                                ? 'received'.tr
                                                : controller
                                                            .ordersListController
                                                            .ordersListModel
                                                            .data?[index]
                                                            .statusId ==
                                                        2
                                                    ? 'onDine'.tr
                                                    : 'completed'.tr,
                                            style: AppTextStyle.darkGray12800,
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            SizedBox(
              height: AppSize.height(30),
            ),
          ],
        ),
      ),
    );
  }
}
