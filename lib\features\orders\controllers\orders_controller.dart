import 'package:get/get.dart';

import '../../auth/login/presentation/getx/controllers/login_controller.dart';
import '../../home/<USER>/getx/controllers/orders_list_controller.dart';

class OrdersController extends GetxController {
  final LoginController loginController = Get.find<LoginController>();
  final OrdersListController ordersListController =
      Get.find<OrdersListController>();
  final RxInt orderStateIndex = 0.obs;
  final RxInt orderTypeIndex = 0.obs;
  final List<String> orderStateList = [
    'all'.tr,
    'completed'.tr,
    'inProgress'.tr,
    'pinding'.tr,
    'new'.tr,
    'cancel'.tr,
  ];
  void changeOrderStateIndex(int index) {
    orderStateIndex.value = index;
  }

  void changeOrderTypeIndex(int index) {
    orderTypeIndex.value = index;
  }
}
