import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/settings_controller.dart';
import 'package:point_of_sale/features/settings/presentation/view/widgets/settings_add_tex_section.dart';
import 'package:point_of_sale/features/settings/presentation/view/widgets/settings_printers_section.dart';
import 'package:point_of_sale/features/settings/presentation/view/widgets/settings_tables_activation_section.dart';

class SettingsPage extends GetView<SettingsController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'settings'.tr,
          style: AppTextStyle.primary18800,
        ),
      ),
      body: ListView(
        children: const [
          SettingsPrintersSection(),
          SettingsAddTexSection(),
          SettingsTablesAvtivationSection(),
        ],
      ),
    );
  }
}
